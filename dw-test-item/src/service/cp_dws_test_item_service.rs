//! CP DWS测试项服务实现
//!
//! 实现CP工艺的DWS层测试项数据处理逻辑

use anyhow::Result;
use chrono::{DateTime, Utc};
use common::ck::ck_operate::CkOperate;
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dws::bin_failitem::BinFailitem;
use common::dto::dws::bin_failitem_index::BinFailitemIndex;
use common::dto::dws::bin_failitem_row::BinFailitemRow;
use common::dto::dws::bin_failitem_index_row::BinFailitemIndexRow;
use common::dto::dws::sub_bin_failitem::SubBinFailitem;
use common::dws::sink::{BinFailitemHandler, BinFailitemIndexHandler};
use common::dws::table::distributed::bin_failitem_index_service::BinFailitemIndexService;
use common::model::constant::upload_type::UploadType;
use common::model::constant::EMPTY;
use common::model::key::lot_key::LotKey;
use common::model::dws_mode::DwsMode;
use common::model::dw_table_enum::DwTableEnum;
use common::model::dw_table_calculate_step::DwTableCalculateStep;
use common::model::table_calculate_info::TableCalculateInfo;
use common::model::constant::dw_layer::DwLayer;
use common::repository::mysql::dw_table_repository::DwTableRepository;
use common::service::dw_table_calculate_record_service::DwTableCalculateRecordService;
use ck_provider::CkConfig;
use mysql_provider::{MySqlConfig, MySqlProviderImpl};
use log::{error, info};
use redis_provider::RedisProvider;
use std::collections::{HashMap, HashSet};
use std::error::Error;
use std::time::Duration;

// 从common模块导入所需结构体
use crate::config::DwTestItemConfig;
use crate::service::bin_failitem_service::BinFailitemService;
use crate::service::test_program_test_order_service::TestProgramTestOrderService;
use common::ck::ck_sink::{CkSink, SinkHandler};
use common::dto::dwd::die_detail_parquet::DieDetailParquet;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dws::dws_service::{DwsService, DwsSubDieDetail, DwsSubFileDetail, DwsSubTestItemDetail};
use common::dws::table::distributed::bin_test_item_index_service::BinTestItemIndexService;
use common::dws::table::distributed::site_test_item_index_service::SiteTestItemIndexService;
use common::dws::table::distributed::test_item_index_service::TestItemIndexService;
use common::model::key::wafer_key::WaferKey;
use common::utils::ck_util::CkUtil;
use common::utils::path;
use parquet_provider::parquet_provider::write_parquet_multi;
use rayon::prelude::*;

// 从common::dws::model导入索引结构体
use ck_provider::{AsyncCkChannel, CkProvider, CkProviderImpl, CkStreamProcessorBuilder, StreamConfig};
use common::dws::model::{
    BinTestItemIndex, BinTestItemIndexRow, SiteTestItemIndex, SiteTestItemIndexRow, TestItemIndex, TestItemIndexRow,
};
use std::sync::Arc;

/// CP DWS测试项服务
pub struct CpDwsTestItemService {
    // DWS结果分区数
    dws_result_partition: i32,
    // 测试区域
    test_area: String,
    // 配置
    properties: DwTestItemConfig,
}

impl CpDwsTestItemService {
    /// 创建新的CP DWS测试项服务实例
    pub fn new(dws_result_partition: i32, test_area: String, properties: DwTestItemConfig) -> Self {
        Self { dws_result_partition, test_area, properties }
    }

    /// 构建SubFileDetail映射 (对应Scala中的FileDetailService.broadcastSubFileDetail)
    fn build_sub_file_detail_map(
        &self,
        dws_die_detail: &[Arc<DwsSubDieDetail>],
    ) -> std::collections::HashMap<i64, DwsSubFileDetail> {
        use std::collections::HashMap;

        let mut file_detail_map:HashMap<i64, DwsSubFileDetail>  = HashMap::new();

        // 按FILE_ID分组，每个FILE_ID取第一个记录来构建SubFileDetail
        for die in dws_die_detail {
            if let Some(file_id) = die.FILE_ID {
                if !file_detail_map.contains_key(&file_id) {
                    let sub_file_detail = DwsSubFileDetail {
                        CUSTOMER: die.CUSTOMER.clone(),
                        SUB_CUSTOMER: die.SUB_CUSTOMER.clone(),
                        UPLOAD_TYPE: die.UPLOAD_TYPE.clone(),
                        FILE_ID: die.FILE_ID,
                        FILE_NAME: die.FILE_NAME.clone(),
                        FILE_TYPE: die.FILE_TYPE.clone(),
                        FACTORY: die.FACTORY.clone(),
                        FACTORY_SITE: die.FACTORY_SITE.clone(),
                        FAB: die.FAB.clone(),
                        FAB_SITE: die.FAB_SITE.clone(),
                        LOT_TYPE: die.LOT_TYPE.clone(),
                        TEST_AREA: die.TEST_AREA.clone(),
                        OFFLINE_RETEST: die.OFFLINE_RETEST.map(|v| v as i32),
                        INTERRUPT: die.INTERRUPT.map(|v| v as i32),
                        DUP_RETEST: die.DUP_RETEST.map(|v| v as i32),
                        BATCH_NUM: die.BATCH_NUM.map(|v| v as i32),
                        LOT_ID: die.LOT_ID.clone(),
                        SBLOT_ID: die.SBLOT_ID.clone(),
                        PROBER_HANDLER_ID: die.PROBER_HANDLER_ID.clone(),
                        TESTER_TYPE: die.TESTER_TYPE.clone(),
                        TEST_STAGE: die.TEST_STAGE.clone(),
                        DEVICE_ID: die.DEVICE_ID.clone(),
                        TEST_PROGRAM: die.TEST_PROGRAM.clone(),
                        TEST_TEMPERATURE: die.TEST_TEMPERATURE.clone(),
                        TEST_PROGRAM_VERSION: die.TEST_PROGRAM_VERSION.clone(),
                        TESTER_NAME: die.TESTER_NAME.clone(),
                        PROBECARD_LOADBOARD_ID: die.PROBECARD_LOADBOARD_ID.clone(),
                        START_TIME: die.START_TIME,
                        END_TIME: die.END_TIME,
                        START_HOUR_KEY: die.START_HOUR_KEY.clone(),
                        START_DAY_KEY: die.START_DAY_KEY.clone(),
                        END_HOUR_KEY: die.END_HOUR_KEY.clone(),
                        END_DAY_KEY: die.END_DAY_KEY.clone(),
                        FLOW_ID: die.FLOW_ID.clone(),
                        RETEST_BIN_NUM: die.RETEST_BIN_NUM.clone(),
                        PROCESS: die.PROCESS.clone(),
                        UPLOAD_TIME: die.UPLOAD_TIME,
                    };
                    file_detail_map.insert(file_id, sub_file_detail);
                }
            }
        }

        file_detail_map
    }

    /// 计算DWS层测试项数据
    pub async fn calculate(
        &self,
        die_detail: &Vec<DieDetailParquet>,
        test_item_detail: &Vec<Vec<SubTestItemDetail>>,
        wafer_key: &WaferKey,
        test_area: &str,
        dws_mode: &str,
        execute_mode: &str,
        run_mode: &str,
        mysql_provider: &MySqlProviderImpl
    ) -> Result<(), Box<dyn Error>> {
        info!("Calculating CP DWS test item for wafer: {}", wafer_key);
        let now = Utc::now();

        let dw_table_repository = DwTableRepository::new(mysql_provider).await?;
        let need_calculate_tables = dw_table_repository.find_all_calculate_table(Some(UploadType::AUTO), None).await?;
        
        let table_calculate_record_service = DwTableCalculateRecordService::new(
            UploadType::AUTO, 
            None, 
            EMPTY.to_string()
        );
        let mut calculate_time_map: HashMap<DwTableEnum, TableCalculateInfo> = HashMap::new();

        // 构建DWS子Die详情
        let dws_die_detail: Vec<Arc<DwsSubDieDetail>> = die_detail
            .into_par_iter()
            .filter_map(|elem| {
                if elem.X_COORD.unwrap_or(0) != -1024 && elem.Y_COORD.unwrap_or(0) != -1024 {
                    Some(Arc::new(DwsService::build_dws_sub_die_detail_from_parquet(elem)))
                } else {
                    None
                }
            })
            .collect();

        // 构建文件详情映射 (对应Scala中的fileDetailMap)
        let file_detail_map: HashMap<i64, DwsSubFileDetail> = self.build_sub_file_detail_map(&dws_die_detail);

        let dws_test_item_detail: Vec<Arc<DwsSubTestItemDetail>> = test_item_detail
            .par_iter()
            .flatten()
            .into_par_iter()
            .filter_map(|elem| {
                if elem.TEST_VALUE.is_some()
                    && elem.TEST_VALUE.unwrap().is_finite()
                    && elem.X_COORD.unwrap_or(0) != -1024
                    && elem.Y_COORD.unwrap_or(0) != -1024
                {
                    Some(Arc::new(DwsService::build_dws_sub_test_item_detail_with_file_info(elem, &file_detail_map)))
                } else {
                    None
                }
            })
            .collect();
       
        // 计算TestProgramTestOrder
        if need_calculate_tables.contains(&DwTableEnum::DimTestProgramTestOrder.get_table()) {
            self.calculate_test_program_test_order(&dws_die_detail, &dws_test_item_detail, wafer_key, &table_calculate_record_service, &mut calculate_time_map,mysql_provider).await?;
        }

        // 计算BinFailitem
        if need_calculate_tables.contains(&DwTableEnum::DimBinFailitem.get_table()) ||   need_calculate_tables.contains(&DwTableEnum::DwsBinFailitemIndex.get_table()){
            self.calculate_bin_failitem(&dws_die_detail, &dws_test_item_detail, wafer_key, &table_calculate_record_service, &mut calculate_time_map, &need_calculate_tables,&now,mysql_provider).await?;
        }
        
        // 计算Bin测试项索引 (对应Scala中的DWS_BIN_TEST_ITEM_INDEX)
        if need_calculate_tables.contains(&DwTableEnum::DwsBinTestItemIndex.get_table()) {
            self.calculate_bin_test_item_index(&dws_test_item_detail, test_area, wafer_key, &table_calculate_record_service, &mut calculate_time_map).await?;
        }

        // 计算站点测试项索引 (对应Scala中的DWS_SITE_TEST_ITEM_INDEX)
        if need_calculate_tables.contains(&DwTableEnum::DwsSiteTestItemIndex.get_table()) {            
            self.calculate_site_test_item_index(&dws_test_item_detail, test_area, wafer_key, &table_calculate_record_service, &mut calculate_time_map).await?;
        }

        // 计算测试项索引 (对应Scala中的DWS_TEST_ITEM_INDEX)
        if need_calculate_tables.contains(&DwTableEnum::DwsTestItemIndex.get_table()) {            
            self.calculate_test_item_index(&dws_test_item_detail, test_area, wafer_key, &table_calculate_record_service, &mut calculate_time_map).await?;
        }

        // 保存表计算记录(耗时、资源)
        if dws_mode == DwsMode::DWS.get_mode() {
            if !dws_die_detail.is_empty() {
                let first_die = &dws_die_detail[0];
                let file_type = first_die.FILE_TYPE.as_ref();
                let mysql_config = self.properties.get_mysql_config();
                
                table_calculate_record_service.save_dw_table_calculate(
                    &wafer_key.customer,
                    &wafer_key.sub_customer,
                    &wafer_key.factory,
                    &wafer_key.factory_site,
                    test_area,
                    &wafer_key.device_id,
                    &wafer_key.lot_id,
                    &wafer_key.wafer_no,
                    file_type,
                    &wafer_key.lot_type,
                    &wafer_key.test_stage,
                    execute_mode,
                    mysql_config,
                    &calculate_time_map,
                    common::model::constant::dw_layer::DwLayer::DWS,
                    run_mode,
                    Some(common::model::constant::upload_type::UploadType::AUTO),
                    mysql_provider
                ).await?;
            }
        }

        info!("CP DWS calculation completed for wafer: {}", wafer_key);
        Ok(())
    }

    async fn calculate_test_program_test_order(
        &self, 
        dws_die_detail: &Vec<Arc<DwsSubDieDetail>>,
        dws_test_item_detail: &Vec<Arc<DwsSubTestItemDetail>>,
        wafer_key: &WaferKey,
        table_calculate_record_service: &DwTableCalculateRecordService,
        calculate_time_map: &mut HashMap<DwTableEnum, TableCalculateInfo>,
        mysql_provider: &MySqlProviderImpl
    ) -> Result<(), Box<dyn Error>> {
        info!("Calculating test program test order for {} die details and {} test item details", 
              dws_die_detail.len(), dws_test_item_detail.len());
        
        // 记录计算开始时间
        table_calculate_record_service.update_dw_table_calculate(
            calculate_time_map,
            DwTableEnum::DimTestProgramTestOrder,
            DwTableCalculateStep::CalculateStart,
            Some(self.properties.dim_db_name.clone()),
        );

        let lot_key = &wafer_key.to_lot_key();
        let mysql_config = self.properties.get_mysql_config();
        let redis_provider = &RedisProvider::new(&self.properties.redis_address, Some(&self.properties.redis_password))?;

        TestProgramTestOrderService::new(self.test_area.clone()).calculate(
            dws_die_detail,
            dws_test_item_detail,
            mysql_config.clone(),
            self.properties.get_ck_config(&self.properties.dim_db_name),
            lot_key,
            &UploadType::AUTO.to_string(),
            redis_provider,
            "",
            &self.properties.dim_db_name,
            self.properties.dim_result_partition,
            &self.properties.index_num_partition,
            mysql_provider
        ).await?;

        // 记录计算结束时间
        table_calculate_record_service.update_dw_table_calculate(
            calculate_time_map,
            DwTableEnum::DimTestProgramTestOrder,
            DwTableCalculateStep::CalculateEnd,
            Some(self.properties.dim_db_name.clone()),
        );

        Ok(())
    }

    async fn calculate_bin_failitem(
        &self, 
        dws_die_detail: &Vec<Arc<DwsSubDieDetail>>,
        dws_test_item_detail: &Vec<Arc<DwsSubTestItemDetail>>,
        wafer_key: &WaferKey,
        table_calculate_record_service: &DwTableCalculateRecordService,
        calculate_time_map: &mut HashMap<DwTableEnum, TableCalculateInfo>,
        need_calculate_tables: &HashSet<String>,
        now: &DateTime<Utc>,
        mysql_provider: &MySqlProviderImpl
    ) -> Result<(), Box<dyn Error>> {
        info!("Calculating bin failitem for {} die details and {} test item details", 
              dws_die_detail.len(), dws_test_item_detail.len());
        
        let lot_key = &wafer_key.to_lot_key();
        let mysql_config = self.properties.get_mysql_config();
        let redis_provider = &RedisProvider::new(&self.properties.redis_address, Some(&self.properties.redis_password))?;

        let sub_bin_failitem: Vec<SubBinFailitem> = BinFailitemIndexService::new(self.test_area.clone()).calculate(
            dws_die_detail,
            dws_test_item_detail
        );

        if need_calculate_tables.contains(&DwTableEnum::DwsBinFailitemIndex.get_table()) {
            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DwsBinFailitemIndex,
                DwTableCalculateStep::CalculateStart,
                Some(self.properties.dws_db_name.clone()),
            );
            let bin_failitem_index = BinFailitemIndexService::new(self.test_area.clone()).cp_calculate(
                &sub_bin_failitem
            );
            info!("Generated {} bin failitem indices", bin_failitem_index.len());
            

            // 写入HDFS (Parquet文件)
            self.write_bin_failitem_index_to_hdfs(&bin_failitem_index, &self.test_area, wafer_key)
                .await?;

            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DwsBinFailitemIndex,
                DwTableCalculateStep::CalculateEnd,
                Some(self.properties.dws_db_name.clone()),
            );
            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DwsBinFailitemIndex,
                DwTableCalculateStep::SinkCkStart,
                Some(self.properties.dws_db_name.clone()),
            );
            
            // 写入ClickHouse
            self.write_bin_failitem_index_to_clickhouse(&bin_failitem_index, wafer_key, &self.test_area, now)
                .await?;

            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DwsBinFailitemIndex,
                DwTableCalculateStep::SinkCkEnd,
                Some(self.properties.dws_db_name.clone()),
            );
            
        }
        
        if need_calculate_tables.contains(&DwTableEnum::DimBinFailitem.get_table()){
            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DimBinFailitem,
                DwTableCalculateStep::CalculateStart,
                Some(self.properties.dim_db_name.clone()),
            );
            let bin_failitem = BinFailitemService::new(self.test_area.clone()).calculate(sub_bin_failitem);
            info!("Generated {} bin failitem records", bin_failitem.len());
    

            // 写入HDFS (Parquet文件)
            self.write_bin_failitem_to_hdfs(&bin_failitem, &self.test_area, wafer_key)
                .await?;

            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DimBinFailitem,
                DwTableCalculateStep::CalculateEnd,
                Some(self.properties.dim_db_name.clone()),
            );
            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DimBinFailitem,
                DwTableCalculateStep::SinkCkStart,
                Some(self.properties.dim_db_name.clone()),
            );
            // 写入ClickHouse
            self.write_bin_failitem_to_clickhouse(&bin_failitem, wafer_key, &self.test_area)
                .await?;

            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DimBinFailitem,
                DwTableCalculateStep::SinkCkEnd,
                Some(self.properties.dim_db_name.clone()),
            );
            
    
            // 计算bin关系并写入MySQL
            BinFailitemService::new(self.test_area.clone()).calculate_bin_relation(
                bin_failitem, 
                mysql_config.clone(), 
                lot_key, 
                &UploadType::AUTO.to_string(),
                 redis_provider,
                 mysql_provider
                ).await?;
        }
        Ok(())
    }

    /// 计算Bin测试项索引
    async fn calculate_bin_test_item_index(
        &self,
        dws_test_item_detail: &[Arc<DwsSubTestItemDetail>],
        test_area: &str,
        wafer_key: &WaferKey,
        table_calculate_record_service: &DwTableCalculateRecordService,
        calculate_time_map: &mut HashMap<DwTableEnum, TableCalculateInfo>,
    ) -> Result<(), Box<dyn Error>> {
        info!("Calculating bin test item index for {} items", dws_test_item_detail.len());
        table_calculate_record_service.update_dw_table_calculate(
            calculate_time_map,
            DwTableEnum::DwsBinTestItemIndex,
            DwTableCalculateStep::CalculateStart,
            Some(self.properties.dws_db_name.clone()),
        );

        let bin_test_item_service = BinTestItemIndexService::new(test_area.to_string());
        let bin_test_item_indices = bin_test_item_service.calculate(dws_test_item_detail, None);

        info!("Generated {} bin test item indices", bin_test_item_indices.len());

        if !bin_test_item_indices.is_empty() {
            // 写入HDFS (Parquet文件)
            self.write_bin_test_item_index_to_hdfs(&bin_test_item_indices, test_area, wafer_key)
                .await?;

            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DwsBinTestItemIndex,
                DwTableCalculateStep::CalculateEnd,
                Some(self.properties.dws_db_name.clone()),
            );
            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DwsBinTestItemIndex,
                DwTableCalculateStep::SinkCkStart,
                Some(self.properties.dws_db_name.clone()),
            );

            info!("写入ClickHouse");
            self.write_bin_test_item_index_to_clickhouse(&bin_test_item_indices, wafer_key, test_area)
                .await?;

            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DwsBinTestItemIndex,
                DwTableCalculateStep::SinkCkEnd,
                Some(self.properties.dws_db_name.clone()),
            );
        }

        Ok(())
    }

    /// 计算站点测试项索引
    async fn calculate_site_test_item_index(
        &self,
        dws_test_item_detail: &[Arc<DwsSubTestItemDetail>],
        test_area: &str,
        wafer_key: &WaferKey,
        table_calculate_record_service: &DwTableCalculateRecordService,
        calculate_time_map: &mut HashMap<DwTableEnum, TableCalculateInfo>,
    ) -> Result<(), Box<dyn Error>> {
        info!("Calculating site test item index for {} items", dws_test_item_detail.len());
        table_calculate_record_service.update_dw_table_calculate(
            calculate_time_map,
            DwTableEnum::DwsSiteTestItemIndex,
            DwTableCalculateStep::CalculateStart,
            Some(self.properties.dws_db_name.clone()),
        );

        let site_test_item_service = SiteTestItemIndexService::new(test_area.to_string());
        let site_test_item_indices = site_test_item_service.calculate(dws_test_item_detail);

        info!("Generated {} site test item indices", site_test_item_indices.len());

        if !site_test_item_indices.is_empty() {
            // 写入HDFS (Parquet文件)
            self.write_site_test_item_index_to_hdfs(&site_test_item_indices, test_area, wafer_key)
                .await?;
  
            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DwsSiteTestItemIndex,
                DwTableCalculateStep::CalculateEnd,
                Some(self.properties.dws_db_name.clone()),
            );
            // 记录Sink开始时间 (在方法内部记录)
            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DwsSiteTestItemIndex,
                DwTableCalculateStep::SinkCkStart,
                Some(self.properties.dws_db_name.clone()),
            );

            info!("写入ClickHouse");
            self.write_site_test_item_index_to_clickhouse(&site_test_item_indices, wafer_key, test_area)
                .await?;

            // 记录Sink结束时间 (在方法内部记录)
            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DwsSiteTestItemIndex,
                DwTableCalculateStep::SinkCkEnd,
                Some(self.properties.dws_db_name.clone()),
            );
        }

        Ok(())
    }

    /// 计算测试项索引
    async fn calculate_test_item_index(
        &self,
        dws_test_item_detail: &[Arc<DwsSubTestItemDetail>],
        test_area: &str,
        wafer_key: &WaferKey,
        table_calculate_record_service: &DwTableCalculateRecordService,
        calculate_time_map: &mut HashMap<DwTableEnum, TableCalculateInfo>,
    ) -> Result<(), Box<dyn Error>> {
        info!("Calculating test item index for {} items", dws_test_item_detail.len());
        table_calculate_record_service.update_dw_table_calculate(
            calculate_time_map,
            DwTableEnum::DwsTestItemIndex,
            DwTableCalculateStep::CalculateStart,
            Some(self.properties.dws_db_name.clone()),
        );
        
        let test_item_service = TestItemIndexService::new(test_area.to_string());
        let test_item_indices = test_item_service.calculate(dws_test_item_detail);

        info!("Generated {} test item indices", test_item_indices.len());

        if !test_item_indices.is_empty() {
            // 写入HDFS (Parquet文件)
            self.write_test_item_index_to_hdfs(&test_item_indices, test_area, wafer_key)
                .await?;

            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DwsTestItemIndex,
                DwTableCalculateStep::CalculateEnd,
                Some(self.properties.dws_db_name.clone()),
            );
            // 记录Sink开始时间 (在方法内部记录)
            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DwsTestItemIndex,
                DwTableCalculateStep::SinkCkStart,
                Some(self.properties.dws_db_name.clone()),
            );

            info!("写入ClickHouse");
            self.write_test_item_index_to_clickhouse(&test_item_indices, wafer_key, test_area)
                .await?;

            // 记录Sink结束时间 (在方法内部记录)
            table_calculate_record_service.update_dw_table_calculate(
                calculate_time_map,
                DwTableEnum::DwsTestItemIndex,
                DwTableCalculateStep::SinkCkEnd,
                Some(self.properties.dws_db_name.clone()),
            );
        }

        Ok(())
    }

    // ========== HDFS写入方法 ==========

    /// 写入BinTestItemIndex到HDFS
    async fn write_bin_test_item_index_to_hdfs(
        &self,
        data: &[BinTestItemIndex],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error>> {
        let table_path = path::get_wafer_path(
            &self.properties.cp_dws_result_dir_template,
            "dws_bin_test_item_index",
            test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        info!("写入BinTestItemIndex parquet文件到路径: {}", table_path);

        // 使用与DWD相同的写入方式，直接写入HDFS
        // 将数据包装成Vec<Vec<T>>格式以匹配write_parquet_multi的期望
        let data_vec = data.to_vec();
        let data_batches = vec![data_vec];
        let data_refs: Vec<&Vec<BinTestItemIndex>> = data_batches.iter().collect();
        write_parquet_multi(
            &table_path,
            &data_refs,
            Some(&self.properties.get_hdfs_config()),
            self.properties.get_batch_size()?, // batch_size
        )
        .await
        .map_err(|e| {
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("写入parquet文件失败: {}", e)))
                as Box<dyn Error>
        })?;
        info!("成功写入BinTestItemIndex parquet文件到路径: {}", table_path);

        Ok(())
    }

    /// 写入SiteTestItemIndex到HDFS
    async fn write_site_test_item_index_to_hdfs(
        &self,
        data: &[SiteTestItemIndex],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error>> {
        let table_path = path::get_wafer_path(
            &self.properties.cp_dws_result_dir_template,
            "dws_site_test_item_index",
            test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        info!("写入SiteTestItemIndex parquet文件到路径: {}", table_path);

        // 使用与DWD相同的写入方式，直接写入HDFS
        // 将数据包装成Vec<Vec<T>>格式以匹配write_parquet_multi的期望
        let data_vec = data.to_vec();
        let data_batches = vec![data_vec];
        let data_refs: Vec<&Vec<SiteTestItemIndex>> = data_batches.iter().collect();
        write_parquet_multi(
            &table_path,
            &data_refs,
            Some(&self.properties.get_hdfs_config()),
            self.properties.get_batch_size()?, // batch_size
        )
        .await
        .map_err(|e| {
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("写入parquet文件失败: {}", e)))
                as Box<dyn Error>
        })?;
        info!("成功写入SiteTestItemIndex parquet文件到路径: {}", table_path);

        Ok(())
    }

    /// 写入TestItemIndex到HDFS
    async fn write_test_item_index_to_hdfs(
        &self,
        data: &[TestItemIndex],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error>> {
        let table_path = path::get_wafer_path(
            &self.properties.cp_dws_result_dir_template,
            "dws_test_item_index",
            test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        info!("写入TestItemIndex parquet文件到路径: {}", table_path);

        // 使用与DWD相同的写入方式，直接写入HDFS
        // 将数据包装成Vec<Vec<T>>格式以匹配write_parquet_multi的期望
        let data_vec = data.to_vec();
        let data_batches = vec![data_vec];
        let data_refs: Vec<&Vec<TestItemIndex>> = data_batches.iter().collect();
        write_parquet_multi(
            &table_path,
            &data_refs,
            Some(&self.properties.get_hdfs_config()),
            self.properties.get_batch_size()?, // batch_size
        )
        .await
        .map_err(|e| {
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("写入parquet文件失败: {}", e)))
                as Box<dyn Error>
        })?;
        info!("成功写入TestItemIndex parquet文件到路径: {}", table_path);

        Ok(())
    }

    /// 写入BinFailitemIndex到HDFS
    async fn write_bin_failitem_index_to_hdfs(
        &self,
        data: &[BinFailitemIndex],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error>> {
        let table_path = path::get_wafer_path(
            &self.properties.cp_dws_result_dir_template,
            &DwTableEnum::DwsBinFailitemIndex.get_dir_table_name(),
            test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        info!("写入BinFailitemIndex parquet文件到路径: {}", table_path);

        let data_vec = data.to_vec();
        let data_batches = vec![data_vec];
        let data_refs: Vec<&Vec<BinFailitemIndex>> = data_batches.iter().collect();
        write_parquet_multi(
            &table_path,
            &data_refs,
            Some(&self.properties.get_hdfs_config()),
            self.properties.get_batch_size()?,
        )
        .await
        .map_err(|e| {
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("写入parquet文件失败: {}", e)))
                as Box<dyn Error>
        })?;
        info!("成功写入BinFailitemIndex parquet文件到路径: {}", table_path);

        Ok(())
    }

    /// 写入BinFailitem到HDFS
    async fn write_bin_failitem_to_hdfs(
        &self,
        data: &[BinFailitem],
        test_area: &str,
        wafer_key: &WaferKey,
    ) -> Result<(), Box<dyn Error>> {
        let table_path = path::get_wafer_path(
            &self.properties.cp_dim_result_dir_template,
            &DwTableEnum::DimBinFailitem.get_dir_table_name(),
            test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        info!("写入BinFailitem parquet文件到路径: {}", table_path);

        let data_vec = data.to_vec();
        let data_batches = vec![data_vec];
        let data_refs: Vec<&Vec<BinFailitem>> = data_batches.iter().collect();
        write_parquet_multi(
            &table_path,
            &data_refs,
            Some(&self.properties.get_hdfs_config()),
            self.properties.get_batch_size()?,
        )
        .await
        .map_err(|e| {
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("写入parquet文件失败: {}", e)))
                as Box<dyn Error>
        })?;
        info!("成功写入BinFailitem parquet文件到路径: {}", table_path);

        Ok(())
    }

    // ========== ClickHouse写入方法 ==========

    /// 写入BinTestItemIndex到ClickHouse
    async fn write_bin_test_item_index_to_clickhouse(
        &self,
        data: &[BinTestItemIndex],
        _wafer_key: &WaferKey,
        _test_area: &str,
    ) -> Result<(), Box<dyn Error>> {
        let ck_config = self.properties.get_ck_config(&self.properties.dws_db_name);
        let ck_provider = CkProviderImpl::new(ck_config);

        info!("开始写入BinTestItemIndex到ClickHouse，数据量: {}", data.len());

        // 转换为ClickHouse实体类
        let clickhouse_rows: Vec<BinTestItemIndexRow> = data
            .iter()
            .map(|hdfs_entity| BinTestItemIndexRow::from_hdfs_entity(hdfs_entity))
            .collect();

        // 分批发送数据
        let batch_start = std::time::Instant::now();
        let mut total_written = 0;

        for (batch_idx, chunk) in clickhouse_rows.chunks(self.properties.get_batch_size()?).enumerate() {
            info!("写入第{}批BinTestItemIndex数据，数量: {}", batch_idx + 1, chunk.len());

            match ck_provider.insert("dws.dws_bin_test_item_index_cluster", chunk).await {
                Ok(_) => {
                    total_written += chunk.len();
                    info!("成功写入第{}批BinTestItemIndex数据", batch_idx + 1);
                }
                Err(e) => {
                    error!("写入第{}批BinTestItemIndex数据失败: {}", batch_idx + 1, e);
                    return Err(Box::new(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        format!("写入BinTestItemIndex到ClickHouse失败: {}", e)
                    )));
                }
            }
        }

        let total_duration = batch_start.elapsed();
        info!("成功写入所有BinTestItemIndex到ClickHouse，总数: {}，总耗时: {:?}", total_written, total_duration);
        Ok(())
    }

    /// 写入SiteTestItemIndex到ClickHouse - 转换为ClickHouse实体类
    async fn write_site_test_item_index_to_clickhouse(
        &self,
        data: &[SiteTestItemIndex],
        wafer_key: &WaferKey,
        test_area: &str,
    ) -> Result<(), Box<dyn Error>> {
        let ck_config = self.properties.get_ck_config(&self.properties.dws_db_name);
        let ck_provider = CkProviderImpl::new(ck_config.clone());

        info!("开始写入SiteTestItemIndex到ClickHouse，数据量: {}", data.len());

        // 转换为ClickHouse实体类
        let clickhouse_rows: Vec<SiteTestItemIndexRow> = data
            .iter()
            .map(|hdfs_entity| SiteTestItemIndexRow::from_hdfs_entity(hdfs_entity))
            .collect();

        // 使用批量写入方式
        let mut total_written = 0;

        for (batch_idx, chunk) in clickhouse_rows.chunks(self.properties.get_batch_size()?).enumerate() {
            info!("写入第{}批SiteTestItemIndex数据，数量: {}", batch_idx + 1, chunk.len());

            match ck_provider.insert("dws.dws_site_test_item_index_cluster", chunk).await {
                Ok(_) => {
                    total_written += chunk.len();
                    info!("成功写入第{}批数据，累计: {}/{}", batch_idx + 1, total_written, clickhouse_rows.len());
                }
                Err(e) => {
                    error!("写入第{}批SiteTestItemIndex数据失败: {}", batch_idx + 1, e);
                    return Err(Box::new(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        format!("写入SiteTestItemIndex到ClickHouse失败: {}", e),
                    )));
                }
            }
        }

        info!("成功写入所有SiteTestItemIndex到ClickHouse，总数: {}", total_written);
        Ok(())
    }

    /// 写入TestItemIndex到ClickHouse - 转换为ClickHouse实体类
    async fn write_test_item_index_to_clickhouse(
        &self,
        data: &[TestItemIndex],
        wafer_key: &WaferKey,
        test_area: &str,
    ) -> Result<(), Box<dyn Error>> {
        let ck_config = self.properties.get_ck_config(&self.properties.dws_db_name);
        let ck_provider = CkProviderImpl::new(ck_config.clone());

        info!("开始写入TestItemIndex到ClickHouse，数据量: {}", data.len());

        // 转换为ClickHouse实体类
        let clickhouse_rows: Vec<TestItemIndexRow> = data
            .iter()
            .map(|hdfs_entity| TestItemIndexRow::from_hdfs_entity(hdfs_entity))
            .collect();

        // 使用批量写入方式
        let mut total_written = 0;

        for (batch_idx, chunk) in clickhouse_rows.chunks(self.properties.get_batch_size()?).enumerate() {
            info!("写入第{}批TestItemIndex数据，数量: {}", batch_idx + 1, chunk.len());

            match ck_provider.insert("dws.dws_test_item_index_cluster", chunk).await {
                Ok(_) => {
                    total_written += chunk.len();
                    info!("成功写入第{}批数据，累计: {}/{}", batch_idx + 1, total_written, clickhouse_rows.len());
                }
                Err(e) => {
                    error!("写入第{}批TestItemIndex数据失败: {}", batch_idx + 1, e);
                    return Err(Box::new(std::io::Error::new(
                        std::io::ErrorKind::Other,
                        format!("写入TestItemIndex到ClickHouse失败: {}", e),
                    )));
                }
            }
        }

        info!("成功写入所有TestItemIndex到ClickHouse，总数: {}", total_written);
        Ok(())
    }

    /// 写入BinFailitemIndex到ClickHouse
    async fn write_bin_failitem_index_to_clickhouse(
        &self,
        data: &[BinFailitemIndex],
        wafer_key: &WaferKey,
        _test_area: &str,
        now: &DateTime<Utc>,
    ) -> Result<(), Box<dyn Error>> {
        info!("开始写入BinFailitemIndex到ClickHouse，数据量: {}", data.len());
        let bin_failitem_index_handler = BinFailitemIndexHandler::new(self.properties.dws_db_name.clone());
        let table_full_name = format!("{}.{}", bin_failitem_index_handler.db_name(), bin_failitem_index_handler.table_name());
        CkOperate::tombstone_ck(
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.test_area,
            &wafer_key.lot_id,
            &wafer_key.lot_type,
            &wafer_key.test_stage,
            &wafer_key.device_id,
            None, // lot_bucket
            &wafer_key.wafer_no,
            &table_full_name,
            &self.properties.pick_random_ck_node_host(),
            &self.properties.get_ck_address_list(),
            &self.properties.ck_username,
            &self.properties.ck_password,
            &bin_failitem_index_handler.partition_expr(),
            Some(now.clone()),
        )
        .await
        .map_err(|e| -> Box<dyn Error> {
            Box::new(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!("Tombstone operation failed for table {}: {}", table_full_name, e),
            ))
        })?;
        // 转换为BinFailitemIndexRow类型
        let row_data: Vec<BinFailitemIndexRow> = data.iter()
            .map(|item| BinFailitemIndexRow::new(item))
            .collect();
            
        let mut ck_config: CkConfig = self.properties.get_ck_config(&self.properties.dws_db_name);
        ck_config.url = self.properties.get_ck_address_for_partition(&CkUtil::get_dws_replace_merge_tree_partition_with_device(
            &wafer_key.customer, &wafer_key.test_area, &wafer_key.factory, &wafer_key.sub_customer, &wafer_key.device_id)
        );
        self.write_to_ck_generic(row_data, bin_failitem_index_handler, ck_config).await.map_err(|e| -> Box<dyn Error> { e })?;
        info!("成功写入所有BinFailitemIndex到ClickHouse，总数: {}", data.len());
        Ok(())
    }

    /// 写入BinFailitem到ClickHouse
    async fn write_bin_failitem_to_clickhouse(
        &self,
        data: &[BinFailitem],
        wafer_key: &WaferKey,
        _test_area: &str,
    ) -> Result<(), Box<dyn Error>> {
        info!("开始写入BinFailitem到ClickHouse，数据量: {}", data.len());
        
        // 转换为BinFailitemRow类型
        let row_data: Vec<BinFailitemRow> = data.iter()
            .map(|item| BinFailitemRow::new(item))
            .collect();
            
        let mut ck_config = self.properties.get_ck_config(&self.properties.dim_db_name);
        let partition = &CkUtil::get_test_program_partition(&wafer_key.customer, &wafer_key.test_area, &wafer_key.factory);
        ck_config.url = self.properties.get_ck_address_for_partition(partition);
        let bin_failitem_handler = BinFailitemHandler::new(self.properties.dim_db_name.clone());
        
        self.write_to_ck_generic(row_data, bin_failitem_handler.clone(), ck_config.clone()).await.map_err(|e| -> Box<dyn Error> { e })?;
        let optimize_sql = CkSink::get_optimize_table_sql(&bin_failitem_handler, partition);
        CkSink::optimize_table(&ck_config, &optimize_sql).await?;
        info!("成功写入所有BinFailitem到ClickHouse，总数: {}", data.len());
        Ok(())
    }

    async fn write_to_ck_generic<T>(
        &self,
        data: Vec<T>,
        handler: impl SinkHandler + Send + Sync,
        ck_config: CkConfig
    ) -> Result<(), Box<dyn Error + Send + Sync>>
    where
        T: clickhouse::Row + serde::Serialize + Send + Sync + Clone + 'static,
    {
        use ck_provider::CkConfig;
        use common::ck::ck_sink::CkSink;

        if data.is_empty() {
            log::info!("No data to write.");
            return Ok(());
        }

        log::info!("Writing {} records to ClickHouse.", data.len());

        // Write to ClickHouse with partition
        CkSink::write_to_ck(&data, 1, &ck_config, &handler, false).await.map_err(
            |e| -> Box<dyn Error + Send + Sync> {
                log::error!("写入clickhouse 失败: {}, 数据量为: {}", handler.table_name(), data.len());
                Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))
            },
        )?;

        log::info!("Successfully wrote {} records to ClickHouse.", data.len());
        Ok(())
    }
}
